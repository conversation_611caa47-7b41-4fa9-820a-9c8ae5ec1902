package com.jdl.aic.admin.controller;

import com.jdl.aic.admin.common.result.Result;
import com.jdl.aic.admin.service.SolutionService;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 解决方案管理控制器
 * 
 * <p>提供解决方案的完整管理功能，包括CRUD操作、步骤管理、批量操作等。
 * 遵循useCrudOperations.js的接口规范，确保前端组件能够正确调用。
 */
@RestController
@RequestMapping("/api/admin/solution")
public class SolutionController {

    @Autowired
    private SolutionService solutionService;

    // ==================== 基础CRUD操作 ====================

    /**
     * 获取解决方案列表（分页）- 兼容useCrudOperations
     * POST /api/admin/solution
     */
    @PostMapping
    public Result<PageResult<SolutionDTO>> getSolutionList(@RequestBody Map<String, Object> params) {
        try {
            // 解析分页参数
            Integer page = (Integer) params.getOrDefault("page", 0);
            Integer size = (Integer) params.getOrDefault("size", 10);
            String sort = (String) params.getOrDefault("sort", "created_at,desc");
            PageRequest pageRequest = new PageRequest(page, size, sort);

            // 解析查询参数
            String category = (String) params.get("category");
            Integer status = (Integer) params.get("status");
            Long authorId = params.get("authorId") != null ? 
                Long.valueOf(params.get("authorId").toString()) : null;
            String search = (String) params.get("search");

            return solutionService.getSolutionList(pageRequest, category, status, authorId, search);
        } catch (Exception e) {
            return Result.failed("获取解决方案列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取解决方案详情
     * GET /api/admin/solution/{id}
     */
    @GetMapping("/{id}")
    public Result<SolutionDTO> getSolutionById(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.failed("解决方案ID不能为空或无效");
            }
            
            SolutionDTO solution = solutionService.getSolutionById(id);
            if (solution != null) {
                return Result.success(solution);
            } else {
                return Result.failed("解决方案不存在");
            }
        } catch (Exception e) {
            return Result.failed("获取解决方案详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建解决方案 - 兼容useCrudOperations
     * POST /api/admin/solution/create
     */
    @PostMapping("/create")
    public Result<SolutionDTO> createSolution(@RequestBody SolutionDTO solution) {
        try {
            if (solution == null) {
                return Result.failed("解决方案信息不能为空");
            }
            
            if (solution.getTitle() == null || solution.getTitle().trim().isEmpty()) {
                return Result.failed("解决方案标题不能为空");
            }

            return solutionService.createSolution(solution);
        } catch (Exception e) {
            return Result.failed("创建解决方案失败：" + e.getMessage());
        }
    }

    /**
     * 更新解决方案 - 兼容useCrudOperations
     * PUT /api/admin/solution/{id}
     */
    @PutMapping("/{id}")
    public Result<SolutionDTO> updateSolution(@PathVariable Long id, @RequestBody SolutionDTO solution) {
        try {
            if (id == null || id <= 0) {
                return Result.failed("解决方案ID不能为空或无效");
            }
            
            if (solution == null) {
                return Result.failed("解决方案信息不能为空");
            }

            // 确保ID一致
            solution.setId(id);
            return solutionService.updateSolution(id, solution);
        } catch (Exception e) {
            return Result.failed("更新解决方案失败：" + e.getMessage());
        }
    }

    /**
     * 删除解决方案 - 兼容useCrudOperations
     * DELETE /api/admin/solution/{id}
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteSolution(@PathVariable Long id) {
        try {
            if (id == null || id <= 0) {
                return Result.failed("解决方案ID不能为空或无效");
            }

            Result<Void> result = solutionService.deleteSolution(id);
            if (result.getCode() == 200) {
                return Result.success("解决方案删除成功");
            } else {
                return Result.failed(result.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("删除解决方案失败：" + e.getMessage());
        }
    }

    // ==================== 批量操作 ====================

    /**
     * 批量删除解决方案 - 兼容useCrudOperations
     * POST /api/admin/solution/batch-delete
     */
    @PostMapping("/batch-delete")
    public Result<String> batchDeleteSolutions(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            
            if (ids == null || ids.isEmpty()) {
                return Result.failed("请选择要删除的解决方案");
            }

            Result<Void> result = solutionService.batchDeleteSolutions(ids);
            if (result.getCode() == 200) {
                return Result.success("批量删除成功，共删除 " + ids.size() + " 个解决方案");
            } else {
                return Result.failed(result.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("批量删除解决方案失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新解决方案状态
     * POST /api/admin/solution/batch-status
     */
    @PostMapping("/batch-status")
    public Result<String> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) request.get("ids");
            Integer status = (Integer) request.get("status");
            
            if (ids == null || ids.isEmpty()) {
                return Result.failed("请选择要更新的解决方案");
            }
            
            if (status == null) {
                return Result.failed("请指定目标状态");
            }

            Result<Void> result = solutionService.batchUpdateStatus(ids, status);
            if (result.getCode() == 200) {
                String statusText = getStatusText(status);
                return Result.success("批量更新成功，共更新 " + ids.size() + " 个解决方案为" + statusText);
            } else {
                return Result.failed(result.getMessage());
            }
        } catch (Exception e) {
            return Result.failed("批量更新状态失败：" + e.getMessage());
        }
    }



    // ==================== 统计和分析 ====================

    /**
     * 获取解决方案分类统计
     * GET /api/admin/solution/stats/category
     */
    @GetMapping("/stats/category")
    public Result<List<Object>> getSolutionCategoryStats() {
        try {
            return solutionService.getSolutionCategoryStats();
        } catch (Exception e) {
            return Result.failed("获取分类统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取热门解决方案列表
     * GET /api/admin/solution/popular
     */
    @GetMapping("/popular")
    public Result<PageResult<SolutionDTO>> getPopularSolutions(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            PageRequest pageRequest = new PageRequest(page, size);
            return solutionService.getPopularSolutions(pageRequest, category, days);
        } catch (Exception e) {
            return Result.failed("获取热门解决方案失败：" + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取状态文本描述
     */
    private String getStatusText(Integer status) {
        switch (status) {
            case 0: return "草稿";
            case 1: return "已发布";
            case 2: return "已下线";
            default: return "未知状态";
        }
    }
}
