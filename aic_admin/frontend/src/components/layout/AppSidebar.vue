<template>
  <!-- 桌面端侧边栏 -->
  <Sidebar
    :class="sidebarClasses"
    v-if="!isMobile"
    role="navigation"
    :aria-label="'主导航菜单'"
    :aria-expanded="!isCollapsed"
  >
    <SidebarHeader class="sidebar-header-premium">
    <div class="flex items-center justify-between h-full">
      <!-- Logo区域 -->
      <div class="flex items-center space-x-3" :class="{ 'justify-center': isCollapsed }">
        <div class="logo-container">
          <div class="logo-icon">
            <MenuIcons name="service" :size="24" />
          </div>
        </div>
        <div v-if="!isCollapsed" class="logo-text">
          <h1 class="logo-title">Admin Pro</h1>
          <p class="logo-subtitle">智能管理控制台</p>
        </div>
      </div>

      <!-- 收起/展开按钮 -->
      <button
        @click="handleMobileMenuToggle"
        class="sidebar-toggle-premium"
        :class="{ 'mx-auto mt-2': isCollapsed }"
        :title="isCollapsed ? '展开侧边栏' : '收起侧边栏'"
        :aria-label="isCollapsed ? '展开侧边栏' : '收起侧边栏'"
      >
        <svg
          class="toggle-icon"
          :class="{ 'rotate-180': isCollapsed }"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>
  </SidebarHeader>

  <SidebarContent class="sidebar-content-premium p-4">
    <!-- 增强搜索功能 -->
    <EnhancedSidebarSearch
      placeholder="搜索菜单... (Ctrl+K)"
      @search="handleSearch"
      @select="handleSearchSelect"
    />

    <!-- 快速访问面板 -->
    <QuickAccessPanel v-if="!isCollapsed" />

    <!-- 主要功能 -->
    <SidebarGroup role="group" :aria-labelledby="'main-functions-label'">
      <SidebarGroupLabel
        :id="'main-functions-label'"
        :collapsible="true"
        class="menu-group-label"
        role="heading"
        :aria-level="2"
      >
        <MenuIcons name="home" :size="16" />
        <span>主要功能</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu role="menu" :aria-labelledby="'main-functions-label'">
          <AccessibleMenuItem
            id="home-menu-item"
            label="首页"
            description="返回系统首页"
            icon="home"
            route="/"
            :is-active="$route.path === '/'"
            @click="announceToScreenReader('已导航到首页')"
          />
          <AccessibleMenuItem
            id="dashboard-menu-item"
            label="数据看板"
            description="查看系统数据统计和图表"
            icon="dashboard"
            route="/dashboard"
            :is-active="$route.name === 'dashboard'"
            :badge="3"
            shortcut="Ctrl+1"
            @click="announceToScreenReader('已导航到数据看板')"
          />
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 分割线 -->
    <div class="sidebar-divider"></div>

    <!-- MCP服务 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="service" :size="16" />
        <span>MCP服务</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <div class="relative">
              <SidebarMenuButton
                :is-active="$route.name === 'services'"
                @click="$router.push('/services')"
                :title="'服务管理'"
                class="sidebar-menu-button-premium"
              >
                <MenuIcons name="service" class="sidebar-menu-icon" />
                <span class="sidebar-menu-text">服务管理</span>
              </SidebarMenuButton>
              <div class="sidebar-menu-badge">12</div>
            </div>
          </SidebarMenuItem>
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'categories'"
              @click="$router.push('/categories')"
              :title="'分类管理'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="category" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">分类管理</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'tags'"
              @click="$router.push('/tags')"
              :title="'标签管理'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="tag" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">标签管理</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 成长地图 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="growth" :size="16" />
        <span>成长地图</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'dev-tools'"
              @click="$router.push('/dev-tools')"
              :title="'研发工具'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="tools" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">研发工具</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'menu-demo'"
              @click="$router.push('/menu-demo')"
              :title="'菜单演示'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="dashboard" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">菜单演示</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 规则中心 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="rules" :size="16" />
        <span>规则中心</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'prompts'"
              @click="$router.push('/prompts')"
              :title="'AI提示词'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="prompts" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">AI提示词</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'solutions'"
              @click="$router.push('/solutions')"
              :title="'解决方案管理'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="solution" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">解决方案管理</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 定时任务 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="tasks" :size="16" />
        <span>定时任务</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'tasks'"
              @click="$router.push('/tasks')"
              :title="'任务管理'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="tasks" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">任务管理</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 分割线 -->
    <div class="sidebar-divider"></div>

    <!-- 控制台 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="users" :size="16" />
        <span>控制台</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'users'"
              @click="$router.push('/users')"
              :title="'用户管理'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="users" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">用户管理</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 个人中心 -->
    <SidebarGroup>
      <SidebarGroupLabel :collapsible="true" class="menu-group-label">
        <MenuIcons name="settings" :size="16" />
        <span>个人中心</span>
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu class="sidebar-menu">
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'wallet'"
              @click="$router.push('/wallet')"
              :title="'钱包'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="wallet" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">钱包</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem class="sidebar-menu-item">
            <SidebarMenuButton
              :is-active="$route.name === 'settings'"
              @click="$router.push('/settings')"
              :title="'个人设置'"
              class="sidebar-menu-button-premium"
            >
              <MenuIcons name="settings" class="sidebar-menu-icon" />
              <span class="sidebar-menu-text">个人设置</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>

    <!-- 底部面板：推荐和统计 -->
    <SidebarFooterPanel v-if="!isCollapsed" />
  </SidebarContent>
  </Sidebar>

  <!-- 移动端菜单覆盖层 -->
  <MobileMenuOverlay
    v-if="isMobile"
    :is-open="isMobileMenuOpen"
    @close="handleMobileMenuClose"
    @search="handleMobileSearch"
  />
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useResponsiveLayout } from '@/composables/useResponsiveLayout'
import { useAccessibility } from '@/composables/useAccessibility'
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarBadge,
  SidebarSearch,
  SidebarDivider,
  useSidebar
} from '@/components/ui/sidebar'
import EnhancedSidebarSearch from '@/components/ui/EnhancedSidebarSearch.vue'
import QuickAccessPanel from '@/components/ui/QuickAccessPanel.vue'
import SidebarFooterPanel from '@/components/ui/SidebarFooterPanel.vue'
import MobileMenuOverlay from '@/components/ui/MobileMenuOverlay.vue'
import AccessibleMenuItem from '@/components/ui/AccessibleMenuItem.vue'
import MenuIcons from '@/components/ui/icons/MenuIcons.vue'

const router = useRouter()
const { isCollapsed, toggleCollapse } = useSidebar()
const themeStore = useThemeStore()
const {
  isMobile,
  isTablet,
  isMobileMenuOpen,
  toggleMobileMenu,
  closeMobileMenu,
  setupGestureHandlers
} = useResponsiveLayout()

const {
  announceToScreenReader,
  setupKeyboardNavigation,
  createSkipLink
} = useAccessibility()

// 搜索功能
const searchQuery = ref('')

const handleSearch = (query) => {
  searchQuery.value = query
  console.log('搜索:', query)
  // 这里可以实现搜索逻辑，比如过滤菜单项
}

const handleSearchSelect = (result) => {
  console.log('选择搜索结果:', result)
  // 导航到选中的页面
  if (result.route) {
    router.push(result.route)
  }
}

// 长按事件处理
const handleLongPress = (routeName) => {
  console.log('长按菜单项:', routeName)
  // 可以显示上下文菜单或快捷操作
}

// 双击事件处理
const handleDoubleClick = (routeName) => {
  console.log('双击菜单项:', routeName)
  // 可以执行快速操作，如在新标签页打开
}

// 响应式相关
const sidebarClasses = computed(() => [
  'sidebar-premium',
  {
    'collapsed': isCollapsed.value && !isMobile.value,
    'mobile-open': isMobileMenuOpen.value && isMobile.value
  }
])

// 移动端菜单处理
const handleMobileMenuToggle = () => {
  if (isMobile.value) {
    toggleMobileMenu()
  } else {
    toggleCollapse()
  }
}

const handleMobileMenuClose = () => {
  closeMobileMenu()
}

const handleMobileSearch = () => {
  // 触发搜索功能
  const searchInput = document.querySelector('.search-input')
  if (searchInput) {
    searchInput.focus()
  }
}

// 生命周期
onMounted(() => {
  // 设置手势处理
  setupGestureHandlers(document.body)

  // 监听全局事件
  window.addEventListener('toggle-sidebar', handleMobileMenuToggle)

  // 创建跳过链接
  createSkipLink('main-content', '跳到主内容')

  // 设置键盘导航
  nextTick(() => {
    const sidebarElement = document.querySelector('.sidebar-premium')
    if (sidebarElement) {
      setupKeyboardNavigation(sidebarElement, {
        orientation: 'vertical',
        wrap: true,
        homeEndKeys: true,
        typeahead: true
      })
    }
  })

  // 公告侧边栏状态
  announceToScreenReader('导航菜单已加载')

  return () => {
    window.removeEventListener('toggle-sidebar', handleMobileMenuToggle)
  }
})
</script>
