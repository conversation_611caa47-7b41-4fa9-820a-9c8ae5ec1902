<template>
  <svg
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    class="solution-icon"
  >
    <!-- 解决方案图标：灯泡+齿轮的组合，代表创新解决方案 -->
    <!-- 灯泡主体 -->
    <path d="M9 21h6" />
    <path d="M12 17v4" />
    <path d="M12 3a6 6 0 0 1 6 6c0 2-1 3.5-3 4l-6 0c-2-.5-3-2-3-4a6 6 0 0 1 6-6z" />
    
    <!-- 齿轮装饰 -->
    <circle cx="18" cy="6" r="2" />
    <path d="M18 4v4" />
    <path d="M16 6h4" />
    
    <!-- 创新火花 -->
    <path d="M8 2l1 2-1 2" />
    <path d="M16 2l-1 2 1 2" />
  </svg>
</template>

<script setup>
// 解决方案图标组件
// 结合了灯泡（创意/想法）和齿轮（实施/执行）的概念
// 代表完整的解决方案从构思到实现的过程
</script>

<style scoped>
.solution-icon {
  width: 100%;
  height: 100%;
}
</style>
