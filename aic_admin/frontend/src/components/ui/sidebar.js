import { defineComponent, h, ref, provide, inject, computed, onMounted, onUnmounted } from 'vue'
import { accessibilityManager } from '@/utils/accessibility'
import { performanceMonitor } from '@/utils/performance'

// Sidebar Context
const SidebarContext = Symbol('SidebarContext')

// Sidebar Provider
export const SidebarProvider = defineComponent({
  name: 'SidebarProvider',
  setup(props, { slots }) {
    const isOpen = ref(true)
    const isCollapsed = ref(false) // 侧边栏本身不收起
    const isMobile = ref(false)

    // 简化的设备检测
    const checkDevice = () => {
      isMobile.value = window.innerWidth <= 1024
    }

    // 简化的切换函数 - 移除isAnimating状态和setTimeout
    const toggleSidebar = () => {
      isOpen.value = !isOpen.value
    }

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
    }

    // 简化的移动端支持
    const handleMobileMenuToggle = () => {
      if (isMobile.value) {
        toggleSidebar()
      }
    }

    // 初始化设备检测
    if (typeof window !== 'undefined') {
      checkDevice()
      window.addEventListener('resize', checkDevice)
    }

    // 性能监控
    onMounted(() => {
      const measure = performanceMonitor.startMeasure('sidebar-mount')

      // 无障碍功能初始化
      accessibilityManager.announce('侧边栏已加载', 'polite')

      // 键盘导航支持
      if (typeof document !== 'undefined') {
        const cleanup = accessibilityManager.enableKeyboardNavigation(document.body)

        onUnmounted(() => {
          cleanup()
          measure.end()
        })
      }
    })

    provide(SidebarContext, {
      isOpen,
      isCollapsed,
      isMobile,
      toggleSidebar,
      toggleCollapse,
      handleMobileMenuToggle
    })

    return () => slots.default?.()
  }
})

// Hook to use sidebar context
export const useSidebar = () => {
  const context = inject(SidebarContext)
  if (!context) {
    throw new Error('useSidebar must be used within SidebarProvider')
  }
  return context
}

// Sidebar Component
export const Sidebar = defineComponent({
  name: 'Sidebar',
  props: {
    class: String
  },
  setup(props, { slots }) {
    const { isOpen, isCollapsed, isMobile } = useSidebar()

    const sidebarClass = computed(() => {
      const baseClass = 'flex flex-col h-full sidebar-premium'
      const stateClass = isCollapsed.value ? 'collapsed' : 'expanded'
      const visibilityClass = isOpen.value ? 'translate-x-0' : '-translate-x-full md:translate-x-0'

      return `${baseClass} ${stateClass} ${visibilityClass} ${props.class || ''}`
    })

    return () => h('aside', {
      class: sidebarClass.value,
      'data-sidebar': 'main',
      'role': 'navigation',
      'aria-label': '主导航',
      'aria-expanded': isOpen.value,
      'aria-hidden': !isOpen.value && isMobile.value
    }, [
      // 跳过链接
      h('a', {
        href: '#main-content',
        class: 'skip-link sr-only focus:not-sr-only'
      }, '跳转到主内容'),
      ...slots.default?.() || []
    ])
  }
})

// Sidebar Header
export const SidebarHeader = defineComponent({
  name: 'SidebarHeader',
  props: {
    class: String
  },
  setup(props, { slots }) {
    return () => h('div', {
      class: `sidebar-header-premium flex items-center justify-between ${props.class || ''}`
    }, slots.default?.())
  }
})

// Sidebar Content
export const SidebarContent = defineComponent({
  name: 'SidebarContent',
  props: {
    class: String
  },
  setup(props, { slots }) {
    return () => h('div', {
      class: `sidebar-content-premium flex-1 ${props.class || ''}`
    }, slots.default?.())
  }
})

// Sidebar Group
export const SidebarGroup = defineComponent({
  name: 'SidebarGroup',
  props: {
    class: String
  },
  setup(props, { slots }) {
    const isExpanded = ref(false) // 默认收起二级菜单

    provide('sidebarGroupExpanded', isExpanded)

    return () => h('div', { class: `mb-4 ${props.class || ''}` }, slots.default?.())
  }
})

// Sidebar Group Label
export const SidebarGroupLabel = defineComponent({
  name: 'SidebarGroupLabel',
  props: {
    class: String,
    collapsible: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { slots }) {
    const { isCollapsed } = useSidebar()
    const groupExpanded = inject('sidebarGroupExpanded', ref(true))

    const handleClick = () => {
      if (props.collapsible && !isCollapsed.value) {
        groupExpanded.value = !groupExpanded.value
      }
    }

    const labelClass = computed(() => {
      const baseClass = 'sidebar-group-label-premium'
      const interactiveClass = props.collapsible && !isCollapsed.value ? 'cursor-pointer' : ''

      return `${baseClass} ${interactiveClass} ${props.class || ''}`
    })

    return () => {
      if (isCollapsed.value) {
        return null // 收起状态下不显示组标签，让菜单项直接显示
      }

      return h('div', {
        class: labelClass.value,
        onClick: handleClick,
        role: props.collapsible ? 'button' : undefined,
        'aria-expanded': props.collapsible ? groupExpanded.value : undefined
      }, [
        ...(slots.default?.() || []), // 直接渲染子内容，不包装在span中
        props.collapsible && h('span', {
          class: `sidebar-group-arrow ${groupExpanded.value ? 'expanded' : ''}`
        }, '▶')
      ])
    }
  }
})

// Sidebar Group Content
export const SidebarGroupContent = defineComponent({
  name: 'SidebarGroupContent',
  props: {
    class: String
  },
  setup(props, { slots }) {
    const { isCollapsed } = useSidebar()
    const groupExpanded = inject('sidebarGroupExpanded', ref(true))

    const contentClass = computed(() => {
      const baseClass = 'sidebar-group-content-premium'
      // 收起状态下始终展开菜单组内容，只有在展开状态下才考虑组的展开状态
      const expandedClass = isCollapsed.value || groupExpanded.value ? 'expanded' : 'collapsed'

      return `${baseClass} ${expandedClass} ${props.class || ''}`
    })

    return () => h('div', {
      class: contentClass.value,
      // 收起状态下不隐藏内容，只有在展开状态下才考虑组的展开状态
      'aria-hidden': !isCollapsed.value && !groupExpanded.value
    }, slots.default?.())
  }
})

// Sidebar Menu
export const SidebarMenu = defineComponent({
  name: 'SidebarMenu',
  props: {
    class: String
  },
  setup(props, { slots }) {
    return () => h('ul', { class: `space-y-1 ${props.class || ''}` }, slots.default?.())
  }
})

// Sidebar Menu Item
export const SidebarMenuItem = defineComponent({
  name: 'SidebarMenuItem',
  props: {
    class: String
  },
  setup(props, { slots }) {
    return () => h('li', { class: props.class }, slots.default?.())
  }
})

// Sidebar Menu Button
export const SidebarMenuButton = defineComponent({
  name: 'SidebarMenuButton',
  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    title: String,
    class: String,
    loading: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { slots, emit }) {
    const { isCollapsed } = useSidebar()

    const buttonClass = computed(() => {
      const baseClass = 'sidebar-menu-button-premium'
      const activeClass = props.isActive ? 'active' : ''
      const disabledClass = props.disabled ? 'opacity-50 cursor-not-allowed' : ''
      // 移除px-2类，使用自定义CSS控制间距
      const spacingClass = isCollapsed.value ? 'justify-center' : ''

      return `${baseClass} ${activeClass} ${disabledClass} ${spacingClass} ${props.class || ''}`
    })

    const handleClick = () => {
      if (!props.disabled && !props.loading) {
        emit('click')
      }
    }

    // 键盘导航支持
    const handleKeyDown = (e) => {
      if (props.disabled || props.loading) return

      // Enter 或 Space 键激活
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleClick()
      }
    }
    const renderContent = () => {
      const content = slots.default?.()
      if (!content) return []

      return [
        ...Array.isArray(content) ? content : [content],

        // 加载状态
        props.loading && h('div', {
          class: 'ml-auto'
        }, [
          h('div', {
            class: 'w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin'
          })
        ]),

        // 工具提示（仅在收起状态显示）
        isCollapsed.value && props.title && h('div', {
          class: 'sidebar-tooltip',
          id: `tooltip-${props.title}`,
          role: 'tooltip'
        }, props.title)
      ].filter(Boolean)
    }

    return () => h('button', {
      class: buttonClass.value,
      onClick: handleClick,
      onKeydown: handleKeyDown,
      disabled: props.disabled || props.loading,
      'aria-label': props.title,
      'aria-current': props.isActive ? 'page' : undefined,
      'aria-disabled': props.disabled || props.loading,
      'aria-busy': props.loading,
      'data-active': props.isActive,
      'role': 'menuitem',
      'tabindex': props.disabled ? -1 : 0,
      // 增强可访问性属性
      'aria-describedby': isCollapsed.value && props.title ? `tooltip-${props.title}` : undefined
    }, renderContent())
  }
})

// Sidebar Badge
export const SidebarBadge = defineComponent({
  name: 'SidebarBadge',
  props: {
    count: {
      type: [Number, String],
      default: 0
    },
    dot: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: 'primary'
    },
    max: {
      type: Number,
      default: 99
    },
    show: {
      type: Boolean,
      default: true
    },
    class: String
  },
  setup(props, { slots }) {
    const badgeClass = computed(() => {
      const baseClass = 'sidebar-badge'
      const sizeClass = props.dot ? 'dot' : ''
      const colorClass = props.color || 'primary'

      return `${baseClass} ${sizeClass} ${colorClass} ${props.class || ''}`
    })

    const displayCount = computed(() => {
      if (props.dot) return ''
      const count = Number(props.count)
      return count > props.max ? `${props.max}+` : count.toString()
    })

    const shouldShow = computed(() => {
      return props.show && (props.dot || Number(props.count) > 0)
    })

    if (!shouldShow.value) return () => null

    return () => h('span', {
      class: badgeClass.value,
      'data-count': props.count
    }, props.dot ? '' : displayCount.value)
  }
})

// Sidebar Search
export const SidebarSearch = defineComponent({
  name: 'SidebarSearch',
  props: {
    placeholder: {
      type: String,
      default: '搜索菜单...'
    },
    class: String
  },
  emits: ['search', 'focus', 'blur'],
  setup(props, { emit }) {
    const { isCollapsed } = useSidebar()
    const searchValue = ref('')
    const isFocused = ref(false)

    const searchClass = computed(() => {
      const baseClass = 'w-full rounded-lg px-3 py-2 text-sm transition-all duration-200 sidebar-search-input'
      const focusClass = isFocused.value ? 'sidebar-search-input-focused' : ''

      return `${baseClass} ${focusClass} ${props.class || ''}`
    })

    const handleInput = (e) => {
      searchValue.value = e.target.value
      emit('search', searchValue.value)
    }

    const handleFocus = () => {
      isFocused.value = true
      emit('focus')
    }

    const handleBlur = () => {
      isFocused.value = false
      emit('blur')
    }

    if (isCollapsed.value) return () => null

    return () => h('div', {
      class: 'relative mb-4'
    }, [
      h('input', {
        type: 'text',
        class: searchClass.value,
        placeholder: props.placeholder,
        value: searchValue.value,
        onInput: handleInput,
        onFocus: handleFocus,
        onBlur: handleBlur
      }),
      h('div', {
        class: 'absolute right-3 top-1/2 transform -translate-y-1/2 sidebar-search-icon'
      }, '🔍')
    ])
  }
})

// Sidebar Divider
export const SidebarDivider = defineComponent({
  name: 'SidebarDivider',
  props: {
    class: String
  },
  setup(props) {
    const { isCollapsed } = useSidebar()

    if (isCollapsed.value) {
      return () => h('div', { class: 'h-4' })
    }

    return () => h('div', {
      class: `sidebar-divider ${props.class || ''}`
    })
  }
})

// Sidebar Gesture Area
export const SidebarGestureArea = defineComponent({
  name: 'SidebarGestureArea',
  setup() {
    const { isMobile, isOpen, handleGestureStart, handleGestureMove, handleGestureEnd } = useSidebar()

    if (!isMobile.value || isOpen.value) return () => null

    return () => h('div', {
      class: 'sidebar-gesture-area',
      onTouchstart: handleGestureStart,
      onTouchmove: handleGestureMove,
      onTouchend: handleGestureEnd,
      onMousedown: handleGestureStart,
      onMousemove: handleGestureMove,
      onMouseup: handleGestureEnd
    }, [
      h('div', { class: 'sidebar-gesture-indicator' })
    ])
  }
})

// Sidebar Overlay - 优化的移动端遮罩层
export const SidebarOverlay = defineComponent({
  name: 'SidebarOverlay',
  setup() {
    const { isMobile, isOpen, toggleSidebar } = useSidebar()

    const overlayClass = computed(() => {
      const baseClass = 'sidebar-overlay'
      const showClass = isOpen.value && isMobile.value ? 'show' : ''
      return `${baseClass} ${showClass}`
    })

    const handleClick = (e) => {
      // 确保点击的是遮罩层本身，而不是其子元素
      if (e.target === e.currentTarget && isMobile.value && isOpen.value) {
        toggleSidebar()
      }
    }

    const handleTouchStart = (e) => {
      // 防止触摸穿透
      if (isMobile.value && isOpen.value) {
        e.preventDefault()
      }
    }

    return () => isMobile.value ? h('div', {
      class: overlayClass.value,
      onClick: handleClick,
      onTouchstart: handleTouchStart,
      'aria-hidden': 'true',
      style: {
        touchAction: 'none' // 防止滚动穿透
      }
    }) : null
  }
})

// Sidebar Trigger - 优化的移动端触发器
export const SidebarTrigger = defineComponent({
  name: 'SidebarTrigger',
  props: {
    class: String,
    size: {
      type: String,
      default: 'md'
    }
  },
  setup(props) {
    const { toggleSidebar, isMobile } = useSidebar()

    const triggerClass = computed(() => {
      const sizeClasses = {
        sm: 'p-2 text-sm',
        md: 'p-3 text-base',
        lg: 'p-4 text-lg'
      }

      const baseClass = `
        rounded-lg
        text-gray-500 hover:text-gray-700
        dark:text-gray-400 dark:hover:text-gray-300
        hover:bg-gray-100 dark:hover:bg-gray-700
        transition-colors
        sidebar-toggle-premium
        ${sizeClasses[props.size]}
        ${isMobile.value ? 'min-h-[44px] min-w-[44px]' : ''}
      `.replace(/\s+/g, ' ').trim()

      return `${baseClass} ${props.class || ''}`
    })

    const handleClick = () => {
      toggleSidebar()
    }

    const handleKeyDown = (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        handleClick()
      }
    }

    return () => h('button', {
      class: triggerClass.value,
      onClick: handleClick,
      onKeydown: handleKeyDown,
      'aria-label': '切换侧边栏',
      'aria-expanded': 'false', // 这个应该由父组件控制
      type: 'button',
      style: {
        touchAction: 'manipulation' // 优化触摸响应
      }
    }, [
      h('span', {
        'aria-hidden': 'true'
      }, '☰')
    ])
  }
})
