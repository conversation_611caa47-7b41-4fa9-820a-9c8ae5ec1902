<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="服务管理"
    description="管理MCP服务和配置，提供完整的服务生命周期管理"
  >
    <template #actions>
      <div class="flex items-center space-x-3">
        <ActionButton variant="secondary" icon="📊" class="hidden sm:flex">
          服务统计
        </ActionButton>
        <ActionButton variant="secondary" icon="📤">
          导出配置
        </ActionButton>
        <ActionButton variant="secondary" icon="📥">
          导入配置
        </ActionButton>
        <ActionButton variant="primary" icon="➕" @click="handleAddService" class="shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
          新增服务
        </ActionButton>
      </div>
    </template>
  </PageHeader>

  <!-- API 状态提示区域 -->
  <div v-if="apiError || dataSource === 'mock'" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4 mb-6">
    <!-- 调试信息 -->
    <div class="text-xs text-gray-500 mb-2">
      当前主题: {{ isDarkMode ? '深色模式' : '浅色模式' }}
    </div>
    <div class="flex items-start space-x-3">
      <div class="flex-shrink-0">
        <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      <div class="flex-1">
        <h3 class="text-sm font-semibold text-yellow-800 dark:text-yellow-200">
          数据来源提示
        </h3>
        <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
          <p v-if="apiError" class="mb-2">
            <span class="font-medium">后台接口调用失败：</span>{{ apiError }}
          </p>
          <p>
            <span class="font-medium">当前数据来源：</span>
            <span v-if="dataSource === 'api'" class="text-green-600 dark:text-green-400">✅ 后台接口数据</span>
            <span v-else class="text-orange-600 dark:text-orange-400">⚠️ 本地模拟数据</span>
          </p>
          <p v-if="dataSource === 'mock'" class="mt-1 text-xs">
            当前显示的是本地模拟数据，可能与实际数据不符。请检查网络连接或联系管理员。
          </p>
        </div>
        <div v-if="apiError" class="mt-3">
          <button
            @click="loadServices"
            :disabled="apiLoading"
            class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-yellow-800/30 dark:text-yellow-200 dark:border-yellow-600 dark:hover:bg-yellow-800/50"
          >
            <svg v-if="apiLoading" class="w-3 h-3 mr-1.5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a7.646 7.646 0 100 15.292V12"></path>
            </svg>
            <svg v-else class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ apiLoading ? '重试中...' : '重试连接' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 搜索筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center space-x-2">
            <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <span>服务名称</span>
          </label>
          <input
            v-model="searchForm.name"
            type="text"
            placeholder="按名称搜索服务..."
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 shadow-sm hover:shadow-md"
          />
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center space-x-2">
            <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <span>服务分类</span>
          </label>
          <select
            v-model="searchForm.category"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <option value="">全部分类</option>
            <option value="image">🖼️ 图像处理</option>
            <option value="text">📝 文本处理</option>
            <option value="translation">🌐 翻译服务</option>
          </select>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center space-x-2">
            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <span>服务标签</span>
          </label>
          <input
            v-model="searchForm.tags"
            type="text"
            placeholder="按标签搜索 (如: API, GraphQL)"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 shadow-sm hover:shadow-md"
          />
        </div>
      </div>
    </template>

    <template #searchActions>
      <div class="flex items-center space-x-3">
        <ActionButton
          variant="search"
          @click="handleSearch"
          :disabled="apiLoading"
          class="shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg v-if="apiLoading" class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a7.646 7.646 0 100 15.292V12"></path>
          </svg>
          <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          {{ apiLoading ? '搜索中...' : '搜索' }}
        </ActionButton>
        <ActionButton
          variant="reset"
          @click="handleReset"
          :disabled="apiLoading"
          class="shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          重置
        </ActionButton>
      </div>
    </template>
  </SearchArea>

  <!-- 服务列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden">
    <!-- 表格头部统计信息 -->
    <div class="px-6 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
      <div class="flex items-center justify-between">
        <span class="text-sm text-gray-600 dark:text-gray-300">
          共 {{ crud.pagination.total || filteredServices.length }} 个服务
        </span>
        <div class="flex items-center space-x-2">
          <div v-if="dataSource === 'api'" class="w-2 h-2 bg-green-500 rounded-full" title="后台接口数据"></div>
          <div v-else class="w-2 h-2 bg-orange-500 rounded-full" title="本地模拟数据"></div>
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ dataSource === 'api' ? 'API' : 'Mock' }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr class="border-b border-gray-200 dark:border-gray-600">
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">服务名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">分类</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">版本</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">更新时间</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <!-- 加载状态 -->
          <tr v-if="apiLoading">
            <td colspan="7" class="px-6 py-8 text-center">
              <div class="flex items-center justify-center space-x-2">
                <svg class="w-5 h-5 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a7.646 7.646 0 100 15.292V12"></path>
                </svg>
                <span class="text-sm text-gray-600 dark:text-gray-300">正在加载...</span>
              </div>
            </td>
          </tr>

          <!-- 无数据状态 -->
          <tr v-else-if="!services || services.length === 0">
            <td colspan="7" class="px-6 py-8 text-center">
              <div class="text-gray-500 dark:text-gray-400">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v1M7 8h10"></path>
                </svg>
                <p class="text-sm">暂无服务数据</p>
              </div>
            </td>
          </tr>

          <!-- 服务列表数据 -->
          <tr v-else v-for="service in services" :key="service.id" class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
            <!-- ID -->
            <td class="px-6 py-4">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ service.id }}</span>
            </td>

            <!-- 服务名称 -->
            <td class="px-6 py-4">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ service.name }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">{{ service.description }}</div>
                </div>
              </div>
            </td>

            <!-- 分类 -->
            <td class="px-6 py-4">
              <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                {{ service.category }}
              </span>
            </td>

            <!-- 版本 -->
            <td class="px-6 py-4">
              <span class="text-sm font-mono text-gray-900 dark:text-white">{{ service.version }}</span>
            </td>

            <!-- 状态 -->
            <td class="px-6 py-4">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                <div class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5"></div>
                {{ service.status }}
              </span>
            </td>

            <!-- 更新时间 -->
            <td class="px-6 py-4">
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ service.updatedAt }}</span>
            </td>
            <!-- 操作 -->
            <td class="px-6 py-4 text-right">
              <div class="flex items-center justify-end space-x-2">
                <button
                  @click="handleViewService(service)"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                  title="查看"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>

                <button
                  @click="handleEditService(service)"
                  class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 p-1"
                  title="编辑"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>

                <button
                  @click="handleDeleteService(service)"
                  class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1"
                  title="删除"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页组件 -->
    <Pagination
      :total="crud.pagination.total"
      :current-page="pagination.currentPage"
      :page-size="pagination.pageSize"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />
  </div>

  <!-- 确认删除弹窗 -->
  <ConfirmModal
    :show="showConfirmModal"
    :title="confirmModalData.title"
    :message="confirmModalData.message"
    :confirm-text="confirmModalData.confirmText"
    :cancel-text="confirmModalData.cancelText"
    @confirm="confirmDelete"
    @close="closeConfirmModal"
  />


  <!-- 编辑弹窗 -->
  <ServiceEdit
    v-if="showEditModal"
    :visible="showEditModal"
    :service="currentEditService"
    @save="handleEditSave"
    @close="handleEditClose"
  />

  <!-- 新增服务对话框 -->
<div v-if="showAddServiceDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="handleCloseAddServiceDialog">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] overflow-hidden">
    <!-- 对话框头部 -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white">新增 MCP 服务</h2>
      <button
        @click="handleCloseAddServiceDialog"
        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- 对话框内容 -->
    <div class="overflow-y-auto max-h-[calc(95vh-140px)]">
      <ServiceDetailDialog
        mode="create"
        :loading="addServiceLoading"
        @save="handleAddServiceSave"
        @cancel="handleAddServiceCancel"
      />
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import ServiceEdit from './ServiceEdit.vue'
import ServiceDetailDialog from '@/components/mcp-service/ServiceDetailDialog.vue'

import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'
import { useThemeStore } from '@/stores/theme'
import { knowledgeApi, post } from '@/utils/api'


const router = useRouter()
const toast = useToast()
const themeStore = useThemeStore()
// 使用 KnowledgeController 的 list 接口
const crud = useCrudOperations('knowledge/list')

// 主题相关
const isDarkMode = computed(() => themeStore.isDarkMode)

const showEditModal = ref(false)
const currentEditService = ref(null)
const editLoading = ref(false)

// API 调用状态
const apiLoading = ref(false)
const apiError = ref(null)
const dataSource = ref('api') // 'api' | 'mock'

// 新增服务对话框状态
const showAddServiceDialog = ref(false)
const addServiceLoading = ref(false)

const searchForm = reactive({
  name: '',
  category: '',
  tags: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 弹窗状态
const showConfirmModal = ref(false)
const confirmModalData = ref({})

const allServices = ref([
  {
    id: 47,
    name: '图像超分辨率服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v2.6.0',
    status: '已发布',
    description: '图像超分辨率',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 48,
    name: '图像去噪服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v1.7.0',
    status: '已发布',
    description: '图像去噪处理',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 49,
    name: '智能图像压缩服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v2.3.0',
    status: '已发布',
    description: '智能图像压缩',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 50,
    name: '批量图像处理服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v1.9.0',
    status: '已发布',
    description: '批量图像处理',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 51,
    name: '文本关键词提取服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v2.1.0',
    status: '已发布',
    description: '文本关键词提取',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 52,
    name: '文本相似度计算',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v1.8.0',
    status: '已发布',
    description: '文本相似度分析',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 53,
    name: '智能文本生成服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v3.0.0',
    status: '已发布',
    description: '智能文本内容生成',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 54,
    name: '代码生成服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v2.4.0',
    status: '已发布',
    description: '智能代码生成',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 55,
    name: '多语言翻译服务',
    category: '文本处理 > 翻译...',
    tags: ['API', 'GraphQL'],
    version: 'v2.8.0',
    status: '已发布',
    description: '多语言智能翻译',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 56,
    name: '专业术语翻译',
    category: '文本处理 > 翻译...',
    tags: ['API', 'GraphQL'],
    version: 'v1.9.0',
    status: '已发布',
    description: '专业术语翻译',
    updatedAt: '2025-07-02T12:07:24'
  }
])

// 过滤后的服务列表
const filteredServices = computed(() => {
  return allServices.value.filter(service => {
    const nameMatch = !searchForm.name || service.name.toLowerCase().includes(searchForm.name.toLowerCase())
    const categoryMatch = !searchForm.category || service.category.includes(searchForm.category)
    const tagsMatch = !searchForm.tags || service.tags.some(tag => tag.toLowerCase().includes(searchForm.tags.toLowerCase()))
    return nameMatch && categoryMatch && tagsMatch
  })
})

// 当前页显示的服务列表（使用CRUD操作的数据）
const services = computed(() => {
  return crud.data.value || []
})

const handleSearch = async () => {
  pagination.currentPage = 1 // 搜索时重置到第一页
  await loadServices()
}

const handleReset = async () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1 // 重置时回到第一页
  await loadServices()
}

const handlePageChange = async (page) => {
  pagination.currentPage = page
  await loadServices()
}

const handlePageSizeChange = async (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
  await loadServices()
}

// 初始化数据
onMounted(async () => {
  // 确保主题已初始化
  themeStore.initTheme()
  await loadServices()
})

// 加载服务列表
const loadServices = async () => {
  // 重置错误状态
  apiError.value = null
  apiLoading.value = true

  try {
    // 构建搜索关键词：合并名称、分类、标签搜索
    let searchKeyword = ''
    const searchTerms = []
    if (searchForm.name) searchTerms.push(searchForm.name)
    if (searchForm.category) searchTerms.push(searchForm.category)
    if (searchForm.tags) searchTerms.push(searchForm.tags)
    searchKeyword = searchTerms.join(' ')

    // 构建请求参数，符合 KnowledgeController#getKnowledgeList 的 KnowledgeListRequest 格式
    const requestParams = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      knowledgeTypeCode: 'mcp', // 固定为 mcp 类型，用于筛选 MCP 服务
      search: searchKeyword, // 使用合并后的搜索关键词
      status: null, // 可以根据需要添加状态筛选
      authorId: null, // 可以根据需要添加作者筛选
      teamId: null // 可以根据需要添加团队筛选
    }

    console.log('调用 /api/knowledge/list 接口，参数:', requestParams)

    // 直接调用 KnowledgeController 的 list 接口
    const response = await post('/knowledge/list', requestParams)

    console.log('API 响应:', response)

    // 详细判断返回值
    if (!response) {
      throw new Error('接口无响应')
    }

    // 判断响应的 code 字段
    if (response.code !== 200) {
      const errorMessage = response.message || `接口调用失败，错误码: ${response.code}`
      throw new Error(errorMessage)
    }

    // 判断数据格式
    if (!response.data) {
      throw new Error('接口返回数据为空')
    }

    // 处理成功响应
    let serviceList = []
    let total = 0

    // 处理 PageResult 格式的数据
    if (response.data.content && Array.isArray(response.data.content)) {
      serviceList = response.data.content
      total = response.data.total || serviceList.length
      pagination.currentPage = (response.data.page || 0) + 1 // 后台从0开始，前台从1开始
    }
    // 处理直接数组格式
    else if (Array.isArray(response.data)) {
      serviceList = response.data
      total = serviceList.length
    }
    // 其他格式
    else {
      console.warn('未知的数据格式:', response.data)
      serviceList = []
      total = 0
    }

    // 更新数据
    crud.data.value = serviceList
    pagination.total = total
    dataSource.value = 'api'

    // 成功提示
    console.log(`✅ 成功从后台获取服务列表数据: ${serviceList.length} 条记录，总计 ${total} 条`)

  } catch (error) {
    console.error('❌ 调用 /api/knowledge/list 接口失败:', error)

    // 记录错误信息
    apiError.value = error.message || '接口调用失败'
    dataSource.value = 'mock'

    // 使用本地mock数据作为备选方案
    let filtered = allServices.value.filter(service => {
      const nameMatch = !searchForm.name || service.name.includes(searchForm.name)
      const categoryMatch = !searchForm.category || service.category.includes(searchForm.category)
      const tagsMatch = !searchForm.tags || service.tags.join(',').includes(searchForm.tags)
      return nameMatch && categoryMatch && tagsMatch
    })

    pagination.total = filtered.length
    const start = (pagination.currentPage - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    crud.data.value = filtered.slice(start, end)

    // 错误提示
    toast.error(`后台接口调用失败: ${apiError.value}，当前显示本地模拟数据`)

  } finally {
    apiLoading.value = false
  }
}

const handleAddService = () => {
  showAddServiceDialog.value = true
}

// 关闭新增服务对话框
const handleCloseAddServiceDialog = () => {
  showAddServiceDialog.value = false
}

// 处理新增服务保存
const handleAddServiceSave = async (data) => {
  addServiceLoading.value = true

  try {
    console.log('开始保存新服务:', data)

    // 构建符合 CreateKnowledgeRequest 格式的请求参数
    const createRequest = {
      knowledgeTypeId: 1, // MCP 服务的知识类型ID
      title: data.name || '未命名服务',
      description: data.summary || '',
      content: data.description || '',
      summary: data.summary || '',
      metadata: {
        // MCP 服务特有的元数据
        category: data.category || '',
        version: data.version || 'v1.0.0',
        status: data.status || 'draft',
        tags: data.tags || [],
        endpoint: data.endpoint || '',
        timeout: data.timeout || 30,
        retries: data.retries || 3,
        config: data.config || {}
      },
      visibility: 'PUBLIC',
      tags: data.tags || []
    }

    console.log('调用 /api/knowledge/create 接口，参数:', createRequest)

    // 调用后台 KnowledgeController 的创建接口
    const response = await post('/knowledge/create', createRequest)

    console.log('创建服务响应:', response)

    // 详细判断返回值
    if (!response) {
      throw new Error('接口无响应')
    }

    if (response.code !== 200) {
      const errorMessage = response.message || `创建失败，错误码: ${response.code}`
      throw new Error(errorMessage)
    }

    if (!response.data) {
      throw new Error('创建成功但返回数据为空')
    }

    // 创建成功
    console.log('✅ 服务创建成功:', response.data)

    toast.success(`服务 "${data.name}" 创建成功`)

    // 关闭对话框
    showAddServiceDialog.value = false

    // 刷新列表
    await loadServices()

  } catch (error) {
    console.error('❌ 创建服务失败:', error)
    toast.error(`创建失败: ${error.message || '未知错误'}`)
  } finally {
    addServiceLoading.value = false
  }
}

// 处理新增服务取消
const handleAddServiceCancel = () => {
  showAddServiceDialog.value = false
}

const handleViewService = (service) => {
  // 跳转到独立详情页面
  router.push({
    name: 'service-detail',
    params: { id: service.id }
  })
}

const handleEditService = async (service) => {
  try {
    editLoading.value = true
    
    // 调用后台API获取完整的服务详情
    const response = await knowledgeApi.getKnowledgeById(service.id)
    
    if (response && response.data) {
      currentEditService.value = response.data
    } else {
      // 如果API调用失败，使用当前服务数据作为备选
      currentEditService.value = { ...service }
      toast.warning('获取服务详情失败，使用当前数据进行编辑')
    }
    
    showEditModal.value = true
  } catch (error) {
    console.error('获取服务详情失败:', error)
    
    // API调用失败时，使用当前服务数据作为备选
    currentEditService.value = { ...service }
    showEditModal.value = true
    
    toast.error('获取服务详情失败，将使用当前数据进行编辑')
  } finally {
    editLoading.value = false
  }
}

const handleEditSave = async (data) => {
  // 此处可调用API更新服务，或直接本地mock替换
  // 假设crud.update用于API更新，allServices用于mock
  try {
    if (crud.update) {
      await crud.update(data)
    } else {
      // mock本地更新
      const idx = allServices.value.findIndex(s => s.id === data.id)
      if (idx > -1) allServices.value[idx] = { ...data }
    }
    toast.success('服务编辑成功')
    showEditModal.value = false
    await loadServices()
  } catch (e) {
    toast.error('服务编辑失败')
  }
}

const handleEditClose = () => {
  showEditModal.value = false
}

const handleDeleteService = (service) => {
  // 使用ConfirmModal确认删除
  confirmModalData.value = {
    title: '确认删除服务',
    message: `确定要删除服务 "${service.name}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    service: service
  }
  showConfirmModal.value = true
}

// 确认删除服务
const confirmDelete = async () => {
  try {
    const service = confirmModalData.value.service
    
    // 调用后台 KnowledgeController#deleteKnowledgeById API
    const response = await knowledgeApi.deleteKnowledge(service.id)
    
    // 判断后台返回的code码，200表示成功，其他表示失败
    if (response && response.code === 200) {
      showConfirmModal.value = false
      
      // 刷新列表
      await loadServices()
      
      toast.success(`服务 "${service.name}" 删除成功`)
    } else {
      // code码不正确，显示删除错误
      const errorMessage = response?.message || '删除服务失败'
      console.error('删除服务失败，code码:', response?.code, '错误信息:', errorMessage)
      toast.error(`删除错误：${errorMessage}`)
    }
  } catch (error) {
    console.error('删除服务失败:', error)
    
    // 网络错误或其他异常
    toast.error('删除服务失败：' + (error.message || '网络错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}


</script>
