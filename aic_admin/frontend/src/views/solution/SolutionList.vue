<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="解决方案管理"
    description="管理和维护AI社区的场景解决方案，提供完整的解决方案生命周期管理"
  >
    <template #actions>
      <div class="flex items-center space-x-3">
        <ActionButton variant="secondary" icon="📊" class="hidden sm:flex">
          解决方案统计
        </ActionButton>
        <ActionButton variant="secondary" icon="📤" @click="handleExport">
          导出数据
        </ActionButton>
        <ActionButton variant="primary" icon="➕" @click="showCreateDialog = true" class="shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
          新建解决方案
        </ActionButton>
      </div>
    </template>
  </PageHeader>

  <!-- 搜索和筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex flex-col sm:flex-row gap-4 flex-1">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            @input="handleSearch"
            type="text"
            placeholder="搜索解决方案标题、描述、作者..."
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
        <div class="flex gap-3">
          <select
            v-model="filters.category"
            @change="handleFilterChange"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">全部分类</option>
            <option value="技术问题">技术问题</option>
            <option value="业务场景">业务场景</option>
            <option value="工具使用">工具使用</option>
            <option value="最佳实践">最佳实践</option>
          </select>

          <select
            v-model="filters.status"
            @change="handleFilterChange"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          >
            <option value="">全部状态</option>
            <option value="0">草稿</option>
            <option value="1">已发布</option>
            <option value="2">已下线</option>
          </select>

          <input
            v-model="filters.authorName"
            @input="handleFilterChange"
            type="text"
            placeholder="作者姓名"
            class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>
    </template>
    <template #searchActions>
      <ActionButton variant="search" @click="fetchList">
        🔍 搜索
      </ActionButton>
      <ActionButton variant="reset" @click="resetFilters">
        🔄 重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 解决方案列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden">
    <!-- 表格头部统计信息 -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">共 {{ pagination.total || data.length }} 个解决方案</span>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            第 {{ pagination.currentPage }} 页，每页 {{ pagination.pageSize }} 条
          </div>
        </div>

        <!-- 批量操作按钮 -->
        <div v-if="selectedItems.length > 0" class="flex items-center space-x-2">
          <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">已选择 {{ selectedItems.length }} 项</span>
          <ActionButton variant="success" size="sm" @click="batchPublish">
            📢 批量发布
          </ActionButton>
          <ActionButton variant="warning" size="sm" @click="batchOffline">
            📴 批量下线
          </ActionButton>
          <ActionButton variant="danger" size="sm" @click="batchDelete">
            🗑️ 批量删除
          </ActionButton>
        </div>
      </div>
    </div>


    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
          <tr class="border-b border-gray-200 dark:border-gray-600">
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
              <div class="flex items-center space-x-2">
                <span>解决方案</span>
                <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
              </div>
            </th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">分类</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">状态</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">作者</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">统计</th>
            <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">创建时间</th>
            <th class="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="8" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center justify-center space-y-3">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span class="text-gray-500 dark:text-gray-400">加载中...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="data.length === 0">
            <td colspan="8" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center justify-center space-y-3">
                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <div class="text-center">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">暂无解决方案</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">点击上方按钮创建第一个解决方案</p>
                </div>
              </div>
            </td>
          </tr>
          <tr
            v-else
            v-for="item in data"
            :key="item.id"
            class="hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 group"
            :class="{ 'bg-blue-50 dark:bg-blue-900/20': selectedItems.includes(item.id) }"
          >
            <td class="px-6 py-4">
              <input
                type="checkbox"
                :checked="selectedItems.includes(item.id)"
                @change="toggleSelect(item.id)"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-500 rounded-lg flex items-center justify-center">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <div>
                  <div class="text-sm font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">{{ item.title }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ item.description || '暂无描述' }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300">
                {{ item.category || '未分类' }}
              </span>
            </td>
            <td class="px-6 py-4">
              <span
                class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                :class="getStatusClass(item.status)"
              >
                {{ getStatusText(item.status) }}
              </span>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900 dark:text-white">{{ item.authorName || '未知' }}</div>
            </td>
            <td class="px-6 py-4">
              <div class="flex flex-col space-y-1">
                <div class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <span>{{ item.viewCount || 0 }}</span>
                </div>
                <div class="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                  <span>{{ item.likeCount || 0 }}</span>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ formatDate(item.createdAt) }}</div>
            </td>
            <td class="px-6 py-4 text-right">
              <div class="flex items-center justify-end space-x-2">
                <button
                  @click="viewSolution(item)"
                  class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  title="查看详情"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
                <button
                  @click="editSolution(item)"
                  class="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  title="编辑"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                </button>
                <button
                  @click="deleteSolution(item)"
                  class="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                  title="删除"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- 分页组件 -->
  <Pagination
    :current-page="pagination.currentPage"
    :total="pagination.total"
    :page-size="pagination.pageSize"
    @page-change="handlePageChange"
  />

  <!-- 创建/编辑对话框 -->
  <SolutionEdit
    v-if="showCreateDialog || showEditDialog"
    :visible="showCreateDialog || showEditDialog"
    :solution="editingSolution"
    :mode="showCreateDialog ? 'create' : 'edit'"
    @close="closeDialog"
    @success="handleSaveSuccess"
  />
</div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import Pagination from '@/components/Pagination.vue'
import SolutionEdit from './SolutionEdit.vue'

export default {
  name: 'SolutionList',
  components: {
    PageHeader,
    SearchArea,
    ActionButton,
    Pagination,
    SolutionEdit
  },
  setup() {
    // 使用统一的CRUD操作
    const {
      data,
      loading,
      pagination,
      fetchList,
      remove,
      batchRemove
    } = useCrudOperations('solution', {
      baseUrl: '/api/admin/solution'
    })

    // 搜索和筛选状态
    const searchQuery = ref('')
    const filters = reactive({
      category: '',
      status: '',
      authorName: ''
    })

    // 选择状态
    const selectedItems = ref([])

    // 对话框状态
    const showCreateDialog = ref(false)
    const showEditDialog = ref(false)
    const editingSolution = ref(null)

    // 计算属性
    const isAllSelected = computed(() => {
      return data.value.length > 0 && selectedItems.value.length === data.value.length
    })

    // 初始化
    onMounted(() => {
      fetchList()
    })

    // 方法
    const viewSolution = (solution) => {
      // 跳转到详情页面或打开详情对话框
      console.log('查看解决方案:', solution)
    }

    const editSolution = (solution) => {
      editingSolution.value = { ...solution }
      showEditDialog.value = true
    }



    const deleteSolution = async (solution) => {
      if (confirm(`确定要删除解决方案"${solution.title}"吗？`)) {
        await remove(solution.id)
      }
    }

    const batchPublish = async () => {
      if (confirm(`确定要批量发布选中的 ${selectedItems.value.length} 个解决方案吗？`)) {
        // 调用批量更新状态接口
        console.log('批量发布:', selectedItems.value)
      }
    }

    const batchOffline = async () => {
      if (confirm(`确定要批量下线选中的 ${selectedItems.value.length} 个解决方案吗？`)) {
        // 调用批量更新状态接口
        console.log('批量下线:', selectedItems.value)
      }
    }

    const batchDelete = async () => {
      if (confirm(`确定要批量删除选中的 ${selectedItems.value.length} 个解决方案吗？此操作不可恢复！`)) {
        await batchRemove(selectedItems.value)
      }
    }

    const closeDialog = () => {
      showCreateDialog.value = false
      showEditDialog.value = false
      editingSolution.value = null
    }

    const handleSaveSuccess = () => {
      closeDialog()
      fetchList() // 刷新列表
    }

    const resetFilters = () => {
      searchQuery.value = ''
      filters.category = ''
      filters.status = ''
      filters.authorName = ''
      fetchList()
    }

    const handlePageChange = (page) => {
      changePage(page)
    }

    const handleExport = () => {
      console.log('导出数据')
      // TODO: 实现导出功能
    }

    // 搜索和筛选方法
    const handleSearch = () => {
      pagination.currentPage = 1 // 重置到第一页
      fetchList({
        search: searchQuery.value,
        category: filters.category,
        status: filters.status,
        authorName: filters.authorName
      })
    }

    const handleFilterChange = () => {
      pagination.currentPage = 1 // 重置到第一页
      fetchList({
        search: searchQuery.value,
        category: filters.category,
        status: filters.status,
        authorName: filters.authorName
      })
    }

    // 选择相关方法
    const toggleSelect = (id) => {
      const index = selectedItems.value.indexOf(id)
      if (index > -1) {
        selectedItems.value.splice(index, 1)
      } else {
        selectedItems.value.push(id)
      }
    }

    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedItems.value = []
      } else {
        selectedItems.value = data.value.map(item => item.id)
      }
    }

    // 分页方法
    const changePage = (page) => {
      pagination.currentPage = page
      fetchList({
        search: searchQuery.value,
        category: filters.category,
        status: filters.status,
        authorName: filters.authorName
      })
    }



    const getStatusClass = (status) => {
      switch (status) {
        case 0: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300'
        case 1: return 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300'
        case 2: return 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300'
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300'
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 0: return '草稿'
        case 1: return '已发布'
        case 2: return '已下线'
        default: return '未知'
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }



    return {
      // 数据
      data,
      loading,
      pagination,
      selectedItems,
      searchQuery,
      filters,

      // 对话框状态
      showCreateDialog,
      showEditDialog,
      editingSolution,

      // 计算属性
      isAllSelected,

      // 方法
      fetchList,
      handleSearch,
      handleFilterChange,
      toggleSelect,
      toggleSelectAll,
      changePage,
      viewSolution,
      editSolution,
      deleteSolution,
      batchPublish,
      batchOffline,
      batchDelete,
      closeDialog,
      handleSaveSuccess,
      resetFilters,
      handlePageChange,
      handleExport,

      // 工具方法
      getStatusClass,
      getStatusText,
      formatDate
    }
  }
}
</script>
