<template>
  <div class="solution-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">解决方案管理</h1>
        <p class="page-description">管理和维护AI社区的场景解决方案</p>
      </div>
      <div class="header-right">
        <button 
          @click="showCreateDialog = true"
          class="btn btn-primary"
        >
          <i class="icon-plus"></i>
          新建解决方案
        </button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <div class="search-box">
        <input
          v-model="searchQuery"
          @input="handleSearch"
          type="text"
          placeholder="搜索解决方案标题、描述..."
          class="search-input"
        />
        <i class="icon-search"></i>
      </div>
      
      <div class="filter-controls">
        <select v-model="filters.category" @change="handleFilterChange" class="filter-select">
          <option value="">全部分类</option>
          <option value="技术问题">技术问题</option>
          <option value="业务场景">业务场景</option>
          <option value="工具使用">工具使用</option>
          <option value="最佳实践">最佳实践</option>
        </select>
        
        <select v-model="filters.status" @change="handleFilterChange" class="filter-select">
          <option value="">全部状态</option>
          <option value="0">草稿</option>
          <option value="1">已发布</option>
          <option value="2">已下线</option>
        </select>
        
        <input
          v-model="filters.authorName"
          @input="handleFilterChange"
          type="text"
          placeholder="作者姓名"
          class="filter-input"
        />
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedItems.length > 0" class="batch-actions">
      <span class="selected-count">已选择 {{ selectedItems.length }} 项</span>
      <div class="action-buttons">
        <button @click="batchPublish" class="btn btn-success btn-sm">
          批量发布
        </button>
        <button @click="batchOffline" class="btn btn-warning btn-sm">
          批量下线
        </button>
        <button @click="batchDelete" class="btn btn-danger btn-sm">
          批量删除
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th class="checkbox-col">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
              />
            </th>
            <th>标题</th>
            <th>分类</th>
            <th>状态</th>
            <th>作者</th>
            <th>步骤数</th>
            <th>浏览量</th>
            <th>点赞数</th>
            <th>创建时间</th>
            <th class="actions-col">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading" class="loading-row">
            <td colspan="10" class="text-center">
              <div class="loading-spinner">加载中...</div>
            </td>
          </tr>
          <tr v-else-if="data.length === 0" class="empty-row">
            <td colspan="10" class="text-center">
              <div class="empty-state">
                <i class="icon-empty"></i>
                <p>暂无解决方案数据</p>
              </div>
            </td>
          </tr>
          <tr 
            v-else
            v-for="item in data" 
            :key="item.id"
            class="data-row"
            :class="{ 'selected': selectedItems.includes(item.id) }"
          >
            <td class="checkbox-col">
              <input
                type="checkbox"
                :checked="selectedItems.includes(item.id)"
                @change="toggleSelect(item.id)"
              />
            </td>
            <td class="title-col">
              <div class="title-content">
                <h4 class="solution-title">{{ item.title }}</h4>
                <p class="solution-description">{{ item.description || '暂无描述' }}</p>
              </div>
            </td>
            <td>
              <span class="category-tag">{{ item.category || '未分类' }}</span>
            </td>
            <td>
              <span 
                class="status-badge"
                :class="getStatusClass(item.status)"
              >
                {{ getStatusText(item.status) }}
              </span>
            </td>
            <td>{{ item.authorName || '未知' }}</td>
            <td>{{ item.steps ? item.steps.length : 0 }}</td>
            <td>{{ item.viewCount || 0 }}</td>
            <td>{{ item.likeCount || 0 }}</td>
            <td>{{ formatDate(item.createdAt) }}</td>
            <td class="actions-col">
              <div class="action-buttons">
                <button 
                  @click="viewSolution(item)"
                  class="btn btn-link btn-sm"
                  title="查看详情"
                >
                  <i class="icon-eye"></i>
                </button>
                <button 
                  @click="editSolution(item)"
                  class="btn btn-link btn-sm"
                  title="编辑"
                >
                  <i class="icon-edit"></i>
                </button>
                <button 
                  @click="manageSolutionSteps(item)"
                  class="btn btn-link btn-sm"
                  title="管理步骤"
                >
                  <i class="icon-steps"></i>
                </button>
                <button 
                  @click="deleteSolution(item)"
                  class="btn btn-link btn-sm text-danger"
                  title="删除"
                >
                  <i class="icon-delete"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <div class="pagination-info">
        共 {{ pagination.total }} 条记录，第 {{ pagination.current + 1 }} / {{ Math.ceil(pagination.total / pagination.size) }} 页
      </div>
      <div class="pagination-controls">
        <button 
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current === 0"
          class="btn btn-outline btn-sm"
        >
          上一页
        </button>
        <span class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            @click="changePage(page - 1)"
            class="btn btn-sm"
            :class="{ 'btn-primary': page - 1 === pagination.current, 'btn-outline': page - 1 !== pagination.current }"
          >
            {{ page }}
          </button>
        </span>
        <button 
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= Math.ceil(pagination.total / pagination.size) - 1"
          class="btn btn-outline btn-sm"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <SolutionEdit
      v-if="showCreateDialog || showEditDialog"
      :visible="showCreateDialog || showEditDialog"
      :solution="editingSolution"
      :mode="showCreateDialog ? 'create' : 'edit'"
      @close="closeDialog"
      @success="handleSaveSuccess"
    />

    <!-- 步骤管理对话框 -->
    <SolutionStepsManager
      v-if="showStepsDialog"
      :visible="showStepsDialog"
      :solution="managingSolution"
      @close="showStepsDialog = false"
      @success="handleStepsSuccess"
    />
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import SolutionEdit from './SolutionEdit.vue'
import SolutionStepsManager from './SolutionStepsManager.vue'

export default {
  name: 'SolutionList',
  components: {
    SolutionEdit,
    SolutionStepsManager
  },
  setup() {
    // 使用统一的CRUD操作
    const {
      data,
      loading,
      pagination,
      selectedItems,
      searchQuery,
      filters,
      fetchList,
      remove,
      batchRemove,
      handleSearch,
      handleFilterChange,
      toggleSelect,
      toggleSelectAll,
      changePage
    } = useCrudOperations('solution')

    // 对话框状态
    const showCreateDialog = ref(false)
    const showEditDialog = ref(false)
    const showStepsDialog = ref(false)
    const editingSolution = ref(null)
    const managingSolution = ref(null)

    // 计算属性
    const isAllSelected = computed(() => {
      return data.value.length > 0 && selectedItems.value.length === data.value.length
    })

    // 初始化
    onMounted(() => {
      fetchList()
    })

    // 方法
    const viewSolution = (solution) => {
      // 跳转到详情页面或打开详情对话框
      console.log('查看解决方案:', solution)
    }

    const editSolution = (solution) => {
      editingSolution.value = { ...solution }
      showEditDialog.value = true
    }

    const manageSolutionSteps = (solution) => {
      managingSolution.value = solution
      showStepsDialog.value = true
    }

    const deleteSolution = async (solution) => {
      if (confirm(`确定要删除解决方案"${solution.title}"吗？`)) {
        await remove(solution.id)
      }
    }

    const batchPublish = async () => {
      if (confirm(`确定要批量发布选中的 ${selectedItems.value.length} 个解决方案吗？`)) {
        // 调用批量更新状态接口
        console.log('批量发布:', selectedItems.value)
      }
    }

    const batchOffline = async () => {
      if (confirm(`确定要批量下线选中的 ${selectedItems.value.length} 个解决方案吗？`)) {
        // 调用批量更新状态接口
        console.log('批量下线:', selectedItems.value)
      }
    }

    const batchDelete = async () => {
      if (confirm(`确定要批量删除选中的 ${selectedItems.value.length} 个解决方案吗？此操作不可恢复！`)) {
        await batchRemove(selectedItems.value)
      }
    }

    const closeDialog = () => {
      showCreateDialog.value = false
      showEditDialog.value = false
      editingSolution.value = null
    }

    const handleSaveSuccess = () => {
      closeDialog()
      fetchList() // 刷新列表
    }

    const handleStepsSuccess = () => {
      showStepsDialog.value = false
      fetchList() // 刷新列表
    }

    const getStatusClass = (status) => {
      switch (status) {
        case 0: return 'status-draft'
        case 1: return 'status-published'
        case 2: return 'status-offline'
        default: return 'status-unknown'
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 0: return '草稿'
        case 1: return '已发布'
        case 2: return '已下线'
        default: return '未知'
      }
    }

    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const getPageNumbers = () => {
      const total = Math.ceil(pagination.total / pagination.size)
      const current = pagination.current
      const pages = []
      
      let start = Math.max(0, current - 2)
      let end = Math.min(total, start + 5)
      
      if (end - start < 5) {
        start = Math.max(0, end - 5)
      }
      
      for (let i = start; i < end; i++) {
        pages.push(i + 1)
      }
      
      return pages
    }

    return {
      // 数据
      data,
      loading,
      pagination,
      selectedItems,
      searchQuery,
      filters,
      
      // 对话框状态
      showCreateDialog,
      showEditDialog,
      showStepsDialog,
      editingSolution,
      managingSolution,
      
      // 计算属性
      isAllSelected,
      
      // 方法
      fetchList,
      handleSearch,
      handleFilterChange,
      toggleSelect,
      toggleSelectAll,
      changePage,
      viewSolution,
      editSolution,
      manageSolutionSteps,
      deleteSolution,
      batchPublish,
      batchOffline,
      batchDelete,
      closeDialog,
      handleSaveSuccess,
      handleStepsSuccess,
      getStatusClass,
      getStatusText,
      formatDate,
      getPageNumbers
    }
  }
}
</script>

<style scoped>
.solution-list-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.search-filter-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  padding: 8px 40px 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.search-box .icon-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  min-width: 120px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  margin-bottom: 16px;
}

.selected-count {
  color: #1d4ed8;
  font-weight: 500;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f9fafb;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.checkbox-col {
  width: 40px;
}

.title-col {
  min-width: 200px;
}

.actions-col {
  width: 120px;
}

.solution-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.solution-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.category-tag {
  display: inline-block;
  padding: 2px 8px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 4px;
  font-size: 12px;
}

.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-draft {
  background: #fef3c7;
  color: #92400e;
}

.status-published {
  background: #d1fae5;
  color: #065f46;
}

.status-offline {
  background: #fee2e2;
  color: #991b1b;
}

.status-unknown {
  background: #f3f4f6;
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-warning {
  background: #f59e0b;
  color: white;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-link {
  background: none;
  color: #6b7280;
  padding: 4px;
}

.btn-link:hover {
  color: #374151;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.text-danger {
  color: #ef4444 !important;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.loading-spinner {
  padding: 40px;
  color: #6b7280;
}

.empty-state {
  padding: 40px;
  text-align: center;
  color: #6b7280;
}

.empty-state .icon-empty {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.data-row.selected {
  background: #eff6ff;
}

.text-center {
  text-align: center;
}
</style>
