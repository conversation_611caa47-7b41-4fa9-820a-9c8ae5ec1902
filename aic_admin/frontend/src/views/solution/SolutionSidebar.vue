<template>
  <div class="solution-sidebar bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-full">
    <!-- 侧边栏头部 -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">解决方案管理</h2>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">快速筛选和管理</p>
    </div>

    <!-- 快速操作 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">快速操作</h3>
      <div class="space-y-2">
        <button 
          @click="$emit('create')"
          class="w-full flex items-center px-3 py-2 text-sm text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          新建解决方案
        </button>
        <button 
          @click="$emit('import')"
          class="w-full flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
          </svg>
          导入解决方案
        </button>
        <button 
          @click="$emit('export')"
          class="w-full flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          导出数据
        </button>
      </div>
    </div>

    <!-- 分类筛选 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">按分类筛选</h3>
      <div class="space-y-1">
        <button 
          v-for="category in categories"
          :key="category.value"
          @click="$emit('filter-category', category.value)"
          class="w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors"
          :class="selectedCategory === category.value 
            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
        >
          <div class="flex items-center">
            <span class="mr-2">{{ category.icon }}</span>
            <span>{{ category.label }}</span>
          </div>
          <span class="text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
            {{ category.count }}
          </span>
        </button>
      </div>
    </div>

    <!-- 状态筛选 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">按状态筛选</h3>
      <div class="space-y-1">
        <button 
          v-for="status in statuses"
          :key="status.value"
          @click="$emit('filter-status', status.value)"
          class="w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors"
          :class="selectedStatus === status.value 
            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
        >
          <div class="flex items-center">
            <div 
              class="w-2 h-2 rounded-full mr-2"
              :class="status.color"
            ></div>
            <span>{{ status.label }}</span>
          </div>
          <span class="text-xs bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
            {{ status.count }}
          </span>
        </button>
      </div>
    </div>

    <!-- 最近操作 -->
    <div class="p-4">
      <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">最近操作</h3>
      <div class="space-y-2">
        <div 
          v-for="recent in recentActions"
          :key="recent.id"
          class="flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer transition-colors"
          @click="$emit('view-recent', recent)"
        >
          <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-500 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ recent.title }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ recent.action }} · {{ formatTime(recent.time) }}</p>
          </div>
        </div>
        
        <div v-if="recentActions.length === 0" class="text-center py-4">
          <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <p class="text-xs text-gray-500 dark:text-gray-400">暂无最近操作</p>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700 mt-auto">
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-3">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-900 dark:text-white">总计</span>
          <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ totalCount }}</span>
        </div>
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div class="text-center">
            <div class="font-medium text-green-600 dark:text-green-400">{{ publishedCount }}</div>
            <div class="text-gray-500 dark:text-gray-400">已发布</div>
          </div>
          <div class="text-center">
            <div class="font-medium text-yellow-600 dark:text-yellow-400">{{ draftCount }}</div>
            <div class="text-gray-500 dark:text-gray-400">草稿</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'SolutionSidebar',
  props: {
    selectedCategory: {
      type: String,
      default: ''
    },
    selectedStatus: {
      type: [String, Number],
      default: ''
    },
    statistics: {
      type: Object,
      default: () => ({
        total: 0,
        published: 0,
        draft: 0,
        offline: 0,
        categories: {}
      })
    },
    recentActions: {
      type: Array,
      default: () => []
    }
  },
  emits: ['create', 'import', 'export', 'filter-category', 'filter-status', 'view-recent'],
  setup(props) {
    const categories = computed(() => [
      { 
        value: '', 
        label: '全部分类', 
        icon: '📋', 
        count: props.statistics.total || 0 
      },
      { 
        value: '技术问题', 
        label: '技术问题', 
        icon: '🔧', 
        count: props.statistics.categories?.['技术问题'] || 0 
      },
      { 
        value: '业务场景', 
        label: '业务场景', 
        icon: '💼', 
        count: props.statistics.categories?.['业务场景'] || 0 
      },
      { 
        value: '工具使用', 
        label: '工具使用', 
        icon: '🛠️', 
        count: props.statistics.categories?.['工具使用'] || 0 
      },
      { 
        value: '最佳实践', 
        label: '最佳实践', 
        icon: '⭐', 
        count: props.statistics.categories?.['最佳实践'] || 0 
      }
    ])

    const statuses = computed(() => [
      { 
        value: '', 
        label: '全部状态', 
        color: 'bg-gray-400', 
        count: props.statistics.total || 0 
      },
      { 
        value: 1, 
        label: '已发布', 
        color: 'bg-green-400', 
        count: props.statistics.published || 0 
      },
      { 
        value: 0, 
        label: '草稿', 
        color: 'bg-yellow-400', 
        count: props.statistics.draft || 0 
      },
      { 
        value: 2, 
        label: '已下线', 
        color: 'bg-red-400', 
        count: props.statistics.offline || 0 
      }
    ])

    const totalCount = computed(() => props.statistics.total || 0)
    const publishedCount = computed(() => props.statistics.published || 0)
    const draftCount = computed(() => props.statistics.draft || 0)

    const formatTime = (time) => {
      if (!time) return ''
      const now = new Date()
      const target = new Date(time)
      const diff = now - target
      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      return target.toLocaleDateString('zh-CN')
    }

    return {
      categories,
      statuses,
      totalCount,
      publishedCount,
      draftCount,
      formatTime
    }
  }
}
</script>
