<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          管理解决方案步骤 - {{ solution?.title }}
        </h2>
        <button @click="$emit('close')" class="close-button">
          <i class="icon-close"></i>
        </button>
      </div>

      <div class="modal-body">
        <!-- 步骤列表 -->
        <div class="steps-container">
          <div class="steps-header">
            <h3 class="steps-title">解决方案步骤</h3>
            <button @click="showAddStepForm = true" class="btn btn-primary btn-sm">
              <i class="icon-plus"></i>
              添加步骤
            </button>
          </div>

          <!-- 添加步骤表单 -->
          <div v-if="showAddStepForm" class="add-step-form">
            <div class="form-group">
              <label class="form-label">步骤标题</label>
              <input
                v-model="newStep.stepTitle"
                type="text"
                class="form-input"
                placeholder="请输入步骤标题"
              />
            </div>
            <div class="form-group">
              <label class="form-label">步骤描述</label>
              <textarea
                v-model="newStep.stepDescription"
                class="form-textarea"
                rows="2"
                placeholder="请输入步骤描述"
              ></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">步骤类型</label>
                <select v-model="newStep.stepType" class="form-select">
                  <option value="">请选择类型</option>
                  <option value="knowledge">知识</option>
                  <option value="tool">工具</option>
                  <option value="action">操作</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">预计时间(分钟)</label>
                <input
                  v-model.number="newStep.estimatedTime"
                  type="number"
                  class="form-input"
                  placeholder="预计时间"
                  min="1"
                />
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">步骤内容</label>
              <textarea
                v-model="newStep.content"
                class="form-textarea"
                rows="4"
                placeholder="请输入步骤的详细内容"
              ></textarea>
            </div>
            <div class="form-actions">
              <button @click="cancelAddStep" class="btn btn-outline btn-sm">
                取消
              </button>
              <button @click="addStep" class="btn btn-primary btn-sm" :disabled="!newStep.stepTitle">
                添加步骤
              </button>
            </div>
          </div>

          <!-- 步骤列表 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner">加载中...</div>
          </div>
          
          <div v-else-if="steps.length === 0" class="empty-state">
            <i class="icon-empty"></i>
            <p>暂无步骤，点击上方按钮添加第一个步骤</p>
          </div>

          <div v-else class="steps-list">
            <div
              v-for="(step, index) in steps"
              :key="step.id"
              class="step-item"
              :class="{ 'editing': editingStep?.id === step.id }"
            >
              <div class="step-header">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-info">
                  <h4 class="step-title">{{ step.stepTitle }}</h4>
                  <p class="step-description">{{ step.stepDescription || '暂无描述' }}</p>
                  <div class="step-meta">
                    <span v-if="step.stepType" class="step-type">{{ getStepTypeText(step.stepType) }}</span>
                    <span v-if="step.estimatedTime" class="step-time">预计 {{ step.estimatedTime }} 分钟</span>
                  </div>
                </div>
                <div class="step-actions">
                  <button @click="moveStepUp(index)" :disabled="index === 0" class="btn btn-link btn-sm" title="上移">
                    <i class="icon-arrow-up"></i>
                  </button>
                  <button @click="moveStepDown(index)" :disabled="index === steps.length - 1" class="btn btn-link btn-sm" title="下移">
                    <i class="icon-arrow-down"></i>
                  </button>
                  <button @click="editStep(step)" class="btn btn-link btn-sm" title="编辑">
                    <i class="icon-edit"></i>
                  </button>
                  <button @click="deleteStep(step)" class="btn btn-link btn-sm text-danger" title="删除">
                    <i class="icon-delete"></i>
                  </button>
                </div>
              </div>
              
              <!-- 步骤内容 -->
              <div v-if="step.content" class="step-content">
                <pre>{{ step.content }}</pre>
              </div>

              <!-- 编辑表单 -->
              <div v-if="editingStep?.id === step.id" class="edit-step-form">
                <div class="form-group">
                  <label class="form-label">步骤标题</label>
                  <input
                    v-model="editingStep.stepTitle"
                    type="text"
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">步骤描述</label>
                  <textarea
                    v-model="editingStep.stepDescription"
                    class="form-textarea"
                    rows="2"
                  ></textarea>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">步骤类型</label>
                    <select v-model="editingStep.stepType" class="form-select">
                      <option value="">请选择类型</option>
                      <option value="knowledge">知识</option>
                      <option value="tool">工具</option>
                      <option value="action">操作</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label class="form-label">预计时间(分钟)</label>
                    <input
                      v-model.number="editingStep.estimatedTime"
                      type="number"
                      class="form-input"
                      min="1"
                    />
                  </div>
                </div>
                <div class="form-group">
                  <label class="form-label">步骤内容</label>
                  <textarea
                    v-model="editingStep.content"
                    class="form-textarea"
                    rows="4"
                  ></textarea>
                </div>
                <div class="form-actions">
                  <button @click="cancelEditStep" class="btn btn-outline btn-sm">
                    取消
                  </button>
                  <button @click="saveStep" class="btn btn-primary btn-sm" :disabled="!editingStep.stepTitle">
                    保存
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-outline">
          关闭
        </button>
        <button @click="saveStepOrders" class="btn btn-primary" :disabled="!hasOrderChanged">
          保存步骤顺序
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { post, put, del } from '@/utils/api'

export default {
  name: 'SolutionStepsManager',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    solution: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'success'],
  setup(props, { emit }) {
    const loading = ref(false)
    const steps = ref([])
    const originalSteps = ref([])
    const showAddStepForm = ref(false)
    const editingStep = ref(null)
    
    // 新步骤表单数据
    const newStep = reactive({
      stepTitle: '',
      stepDescription: '',
      stepType: '',
      estimatedTime: null,
      content: ''
    })

    // 计算属性
    const hasOrderChanged = computed(() => {
      if (steps.value.length !== originalSteps.value.length) return true
      return steps.value.some((step, index) => {
        const original = originalSteps.value[index]
        return !original || step.id !== original.id
      })
    })

    // 监听solution变化，加载步骤数据
    watch(() => props.solution, (newSolution) => {
      if (newSolution && props.visible) {
        loadSteps()
      }
    }, { immediate: true })

    // 方法
    const loadSteps = async () => {
      if (!props.solution?.id) return
      
      loading.value = true
      try {
        const response = await fetch(`/api/admin/solution/${props.solution.id}/steps`)
        const result = await response.json()
        if (result.code === 200) {
          steps.value = result.data || []
          originalSteps.value = [...steps.value]
        }
      } catch (error) {
        console.error('加载步骤失败:', error)
      } finally {
        loading.value = false
      }
    }

    const handleOverlayClick = () => {
      emit('close')
    }

    const resetNewStepForm = () => {
      Object.assign(newStep, {
        stepTitle: '',
        stepDescription: '',
        stepType: '',
        estimatedTime: null,
        content: ''
      })
    }

    const cancelAddStep = () => {
      showAddStepForm.value = false
      resetNewStepForm()
    }

    const addStep = async () => {
      if (!newStep.stepTitle || !props.solution?.id) return

      try {
        const stepData = {
          ...newStep,
          solutionId: props.solution.id,
          stepOrder: steps.value.length + 1
        }
        
        const response = await post('/api/admin/solution/step/create', stepData)
        if (response.code === 200) {
          steps.value.push(response.data)
          cancelAddStep()
        }
      } catch (error) {
        console.error('添加步骤失败:', error)
      }
    }

    const editStep = (step) => {
      editingStep.value = { ...step }
    }

    const cancelEditStep = () => {
      editingStep.value = null
    }

    const saveStep = async () => {
      if (!editingStep.value?.stepTitle) return

      try {
        const response = await put(`/api/admin/solution/step/${editingStep.value.id}`, editingStep.value)
        if (response.code === 200) {
          const index = steps.value.findIndex(s => s.id === editingStep.value.id)
          if (index !== -1) {
            steps.value[index] = { ...response.data }
          }
          editingStep.value = null
        }
      } catch (error) {
        console.error('保存步骤失败:', error)
      }
    }

    const deleteStep = async (step) => {
      if (!confirm(`确定要删除步骤"${step.stepTitle}"吗？`)) return

      try {
        const response = await del(`/api/admin/solution/step/${step.id}`)
        if (response.code === 200) {
          const index = steps.value.findIndex(s => s.id === step.id)
          if (index !== -1) {
            steps.value.splice(index, 1)
          }
        }
      } catch (error) {
        console.error('删除步骤失败:', error)
      }
    }

    const moveStepUp = (index) => {
      if (index > 0) {
        const temp = steps.value[index]
        steps.value[index] = steps.value[index - 1]
        steps.value[index - 1] = temp
      }
    }

    const moveStepDown = (index) => {
      if (index < steps.value.length - 1) {
        const temp = steps.value[index]
        steps.value[index] = steps.value[index + 1]
        steps.value[index + 1] = temp
      }
    }

    const saveStepOrders = async () => {
      if (!hasOrderChanged.value || !props.solution?.id) return

      try {
        const stepOrders = steps.value.map((step, index) => ({
          id: step.id,
          stepOrder: index + 1
        }))
        
        const response = await post(`/api/admin/solution/${props.solution.id}/steps/reorder`, stepOrders)
        if (response.code === 200) {
          originalSteps.value = [...steps.value]
          emit('success')
        }
      } catch (error) {
        console.error('保存步骤顺序失败:', error)
      }
    }

    const getStepTypeText = (type) => {
      switch (type) {
        case 'knowledge': return '知识'
        case 'tool': return '工具'
        case 'action': return '操作'
        default: return type
      }
    }

    return {
      loading,
      steps,
      showAddStepForm,
      editingStep,
      newStep,
      hasOrderChanged,
      handleOverlayClick,
      cancelAddStep,
      addStep,
      editStep,
      cancelEditStep,
      saveStep,
      deleteStep,
      moveStepUp,
      moveStepDown,
      saveStepOrders,
      getStepTypeText
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 95%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.steps-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.add-step-form,
.edit-step-form {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 12px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  font-family: inherit;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.loading-spinner {
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.empty-state .icon-empty {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  transition: all 0.2s;
}

.step-item.editing {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.step-description {
  color: #6b7280;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.step-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.step-type {
  background: #eff6ff;
  color: #1d4ed8;
  padding: 2px 6px;
  border-radius: 3px;
}

.step-time {
  color: #6b7280;
}

.step-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.step-content {
  padding: 0 16px 16px 60px;
  border-top: 1px solid #f3f4f6;
  margin-top: 12px;
  padding-top: 12px;
}

.step-content pre {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.edit-step-form {
  margin: 12px 16px 16px 60px;
  background: #f8fafc;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(:disabled) {
  background: #f9fafb;
}

.btn-link {
  background: none;
  color: #6b7280;
  padding: 4px;
}

.btn-link:hover:not(:disabled) {
  color: #374151;
  background: #f3f4f6;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.text-danger {
  color: #ef4444 !important;
}

.text-danger:hover {
  color: #dc2626 !important;
}
</style>
